import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { catchError, tap, switchMap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { LocalStorageService, TokenData } from './local-storage.service';
import { environment } from '../../../app/environments/environment';

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  refresh: string;
  access: string;
  user_id: number;
  email: string;
  is_staff: boolean;
}

export interface RefreshTokenResponse {
  access: string;
}

export interface VerifyTokenResponse {
  token_type: string;
  exp: number;
  iat: number;
  jti: string;
  user_id: number;
}

export interface UserProfile {
  id: number;
  email: string;
  is_staff: boolean;
}

export interface RegisterRequest {
  email: string;
  password: string;
}

export interface RegisterResponse {
  email: string;
}

export interface VerifyCodeRequest {
  email: string;
  code: string;
}

export interface VerifyCodeResponse {
  detail: string;
}

export interface VerifyCodeError {
  non_field_errors: string[];
}

export interface RegisterError {
  email?: string[];
  password?: string[];
  non_field_errors?: string[];
}

export interface ResendCodeRequest {
  email: string;
}

export interface ResendCodeResponse {
  detail: string;
}

export interface ResendCodeError {
  email?: string[];
  non_field_errors?: string[];
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<any>(null);
  public currentUser$ = this.currentUserSubject.asObservable();
  
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  constructor(
    private http: HttpClient,
    private localStorageService: LocalStorageService,
    private router: Router
  ) {
    // Check if user is already logged in on service initialization
    this.checkAuthenticationStatus();
  }

  private checkAuthenticationStatus(): void {
    const userData = this.localStorageService.getUserData();
    const hasValidToken = this.localStorageService.hasValidToken();
    
    if (userData && hasValidToken) {
      this.currentUserSubject.next(userData);
      this.isAuthenticatedSubject.next(true);
    }
  }

  login(credentials: LoginRequest): Observable<LoginResponse> {
    return this.http.post<LoginResponse>(`${environment.apiUrl}/api/token/`, credentials)
      .pipe(
        tap(response => {
          // Store tokens and user data
          this.localStorageService.setTokens(response);
          
          // Update authentication state
          const userData = {
            user_id: response.user_id,
            email: response.email,
            is_staff: response.is_staff
          };
          
          this.currentUserSubject.next(userData);
          this.isAuthenticatedSubject.next(true);
        }),
        catchError(this.handleError)
      );
  }

  refreshToken(): Observable<RefreshTokenResponse> {
    const refreshToken = this.localStorageService.getRefreshToken();

    if (!refreshToken) {
      return throwError(() => new Error('No refresh token available'));
    }

    return this.http.post<RefreshTokenResponse>(`${environment.apiUrl}/api/token/refresh/`, {
      refresh: refreshToken
    }).pipe(
      tap(response => {
        this.localStorageService.updateAccessToken(response.access);
      }),
      catchError(error => {
        // If refresh fails, logout user
        this.logout();
        return throwError(() => error);
      })
    );
  }

  verifyToken(token?: string): Observable<VerifyTokenResponse> {
    const tokenToVerify = token || this.localStorageService.getAccessToken();

    if (!tokenToVerify) {
      return throwError(() => new Error('No token available to verify'));
    }

    return this.http.post<VerifyTokenResponse>(`${environment.apiUrl}/api/token/verify/`, {
      token: tokenToVerify
    }).pipe(
      catchError(error => {
        if (error.status === 401) {
          // Token is invalid, try to refresh
          return this.refreshToken().pipe(
            switchMap(() => this.verifyToken()),
            catchError(() => {
              this.logout();
              return throwError(() => new Error('Token verification failed'));
            })
          );
        }
        return throwError(() => error);
      })
    );
  }

  getUserProfile(): Observable<UserProfile> {
    return this.http.get<UserProfile>(`${environment.apiUrl}/api/userprofile/`).pipe(
      tap(profile => {
        // Update current user data with fresh profile info
        this.currentUserSubject.next(profile);

        // Update localStorage with fresh user data
        const currentTokens = {
          access: this.localStorageService.getAccessToken() || '',
          refresh: this.localStorageService.getRefreshToken() || '',
          user_id: profile.id,
          email: profile.email,
          is_staff: profile.is_staff
        };
        this.localStorageService.setTokens(currentTokens);
      }),
      catchError(this.handleError)
    );
  }

  register(credentials: RegisterRequest): Observable<RegisterResponse> {
    return this.http.post<RegisterResponse>(`${environment.apiUrl}/api/auth/register/`, credentials).pipe(
      catchError(this.handleError)
    );
  }

  verifyCode(verificationData: VerifyCodeRequest): Observable<VerifyCodeResponse> {
    return this.http.post<VerifyCodeResponse>(`${environment.apiUrl}/api/auth/verify-code/`, verificationData).pipe(
      catchError(this.handleError)
    );
  }

  resendCode(resendData: ResendCodeRequest): Observable<ResendCodeResponse> {
    return this.http.post<ResendCodeResponse>(`${environment.apiUrl}/api/auth/resend-code/`, resendData).pipe(
      catchError(this.handleError)
    );
  }

  logout(): void {
    this.localStorageService.clearTokens();
    this.currentUserSubject.next(null);
    this.isAuthenticatedSubject.next(false);
    this.router.navigate(['/login']);
  }

  isAuthenticated(): boolean {
    return this.localStorageService.hasValidToken();
  }

  getCurrentUser(): any {
    return this.currentUserSubject.value;
  }

  getAccessToken(): string | null {
    return this.localStorageService.getAccessToken();
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = error.error.message;
    } else {
      // Server-side error
      if (error.status === 401) {
        errorMessage = 'Invalid email or password';
      } else if (error.status === 400) {
        errorMessage = 'Please check your input and try again';
      } else if (error.status === 500) {
        errorMessage = 'Server error. Please try again later';
      } else {
        errorMessage = `Error: ${error.message}`;
      }
    }
    
    return throwError(() => new Error(errorMessage));
  }
}
