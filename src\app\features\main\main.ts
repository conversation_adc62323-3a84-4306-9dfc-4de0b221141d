import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { LoadingService } from '../../core/services/loading.service';

@Component({
  selector: 'app-main',
  standalone: false,
  templateUrl: './main.html',
  styleUrl: './main.css'
})
export class Main implements OnInit, OnDestroy {
  isPageLoading = true;
  private loadingSubscription?: Subscription;

  constructor(private loadingService: LoadingService) {}

  ngOnInit(): void {
    // Simulate page content loading
    this.loadingService.setLoading('main-page', true);

    // Simulate loading time for page content
    setTimeout(() => {
      this.loadingService.setLoading('main-page', false);
      this.isPageLoading = false;
    }, 800);

    // Subscribe to main page loading state
    this.loadingSubscription = this.loadingService.getLoading('main-page').subscribe(
      loading => this.isPageLoading = loading
    );
  }

  ngOnDestroy(): void {
    this.loadingSubscription?.unsubscribe();
    this.loadingService.clear('main-page');
  }
}
