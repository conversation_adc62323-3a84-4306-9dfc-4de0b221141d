<div 
  *ngIf="isLoading" 
  class="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm loading-overlay">
  
  <!-- Loading Content -->
  <div class="text-center">
    <!-- Main Spinner -->
    <div class="inline-flex items-center px-8 py-6 bg-black/40 backdrop-blur-md border border-purple-400/30 rounded-xl shadow-2xl">
      <!-- Large Spinner -->
      <svg 
        class="animate-spin h-8 w-8 text-purple-400 mr-4" 
        xmlns="http://www.w3.org/2000/svg" 
        fill="none" 
        viewBox="0 0 24 24">
        <circle 
          class="opacity-25" 
          cx="12" 
          cy="12" 
          r="10" 
          stroke="currentColor" 
          stroke-width="4">
        </circle>
        <path 
          class="opacity-75" 
          fill="currentColor" 
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
        </path>
      </svg>
      
      <!-- Loading Text -->
      <div class="text-white">
        <div class="text-lg font-semibold">Загрузка страницы...</div>
        <div class="text-sm text-gray-300 mt-1">Пожалуйста, подождите</div>
      </div>
    </div>

    <!-- Progress Bar -->
    <div class="mt-6 w-64 mx-auto">
      <div class="w-full bg-gray-700/50 rounded-full h-1">
        <div class="bg-gradient-to-r from-purple-400 to-pink-400 h-1 rounded-full loading-progress"></div>
      </div>
    </div>
  </div>
</div>
