import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Router, NavigationStart, NavigationEnd, NavigationCancel, NavigationError } from '@angular/router';
import { Subscription } from 'rxjs';
import { LoadingService } from '../../../core/services/loading.service';

@Component({
  selector: 'app-global-loading',
  standalone: false,
  templateUrl: './global-loading.html',
  styleUrl: './global-loading.css'
})
export class GlobalLoading implements OnInit, OnDestroy {
  isLoading = false;
  private routerSubscription?: Subscription;
  private loadingSubscription?: Subscription;

  constructor(
    private router: Router,
    private loadingService: LoadingService
  ) {}

  ngOnInit(): void {
    // Subscribe to router events for page transitions
    this.routerSubscription = this.router.events.subscribe(event => {
      if (event instanceof NavigationStart) {
        this.loadingService.setGlobalLoading(true);
      } else if (
        event instanceof NavigationEnd ||
        event instanceof NavigationCancel ||
        event instanceof NavigationError
      ) {
        // Add a small delay to prevent flashing
        setTimeout(() => {
          this.loadingService.setGlobalLoading(false);
        }, 300);
      }
    });

    // Subscribe to global loading state
    this.loadingSubscription = this.loadingService.globalLoading$.subscribe(
      loading => this.isLoading = loading
    );
  }

  ngOnDestroy(): void {
    this.routerSubscription?.unsubscribe();
    this.loadingSubscription?.unsubscribe();
  }
}
