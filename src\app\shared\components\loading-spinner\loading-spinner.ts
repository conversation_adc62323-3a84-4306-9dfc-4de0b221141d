import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-loading-spinner',
  standalone: false,
  templateUrl: './loading-spinner.html',
  styleUrl: './loading-spinner.css'
})
export class LoadingSpinner {
  @Input() message: string = 'Загрузка...';
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() overlay: boolean = false;
  @Input() fullScreen: boolean = false;

  get spinnerClasses(): string {
    const baseClasses = 'animate-spin text-white';
    
    switch (this.size) {
      case 'small':
        return `${baseClasses} h-4 w-4`;
      case 'large':
        return `${baseClasses} h-8 w-8`;
      default:
        return `${baseClasses} h-5 w-5`;
    }
  }

  get containerClasses(): string {
    let classes = 'flex items-center justify-center';
    
    if (this.fullScreen) {
      classes += ' fixed inset-0 z-50 bg-black/50 backdrop-blur-sm';
    } else if (this.overlay) {
      classes += ' absolute inset-0 z-10 bg-black/30 backdrop-blur-sm';
    }
    
    return classes;
  }

  get contentClasses(): string {
    const baseClasses = 'inline-flex items-center px-4 py-2 bg-black/30 backdrop-blur-sm border border-purple-400/30 rounded-lg';
    
    switch (this.size) {
      case 'small':
        return `${baseClasses} px-3 py-1.5 text-sm`;
      case 'large':
        return `${baseClasses} px-6 py-3 text-lg`;
      default:
        return baseClasses;
    }
  }
}
